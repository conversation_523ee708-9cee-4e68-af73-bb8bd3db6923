<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DatabaseMigrationService;

class ConditionalColumnSetValue extends \Google\Model
{
  /**
   * @var array[]
   */
  public $customFeatures;
  protected $sourceNumericFilterType = SourceNumericFilter::class;
  protected $sourceNumericFilterDataType = '';
  protected $sourceTextFilterType = SourceTextFilter::class;
  protected $sourceTextFilterDataType = '';
  protected $valueTransformationType = ValueTransformation::class;
  protected $valueTransformationDataType = '';

  /**
   * @param array[]
   */
  public function setCustomFeatures($customFeatures)
  {
    $this->customFeatures = $customFeatures;
  }
  /**
   * @return array[]
   */
  public function getCustomFeatures()
  {
    return $this->customFeatures;
  }
  /**
   * @param SourceNumericFilter
   */
  public function setSourceNumericFilter(SourceNumericFilter $sourceNumericFilter)
  {
    $this->sourceNumericFilter = $sourceNumericFilter;
  }
  /**
   * @return SourceNumericFilter
   */
  public function getSourceNumericFilter()
  {
    return $this->sourceNumericFilter;
  }
  /**
   * @param SourceTextFilter
   */
  public function setSourceTextFilter(SourceTextFilter $sourceTextFilter)
  {
    $this->sourceTextFilter = $sourceTextFilter;
  }
  /**
   * @return SourceTextFilter
   */
  public function getSourceTextFilter()
  {
    return $this->sourceTextFilter;
  }
  /**
   * @param ValueTransformation
   */
  public function setValueTransformation(ValueTransformation $valueTransformation)
  {
    $this->valueTransformation = $valueTransformation;
  }
  /**
   * @return ValueTransformation
   */
  public function getValueTransformation()
  {
    return $this->valueTransformation;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ConditionalColumnSetValue::class, 'Google_Service_DatabaseMigrationService_ConditionalColumnSetValue');
