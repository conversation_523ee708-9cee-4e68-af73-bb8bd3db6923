<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DatabaseMigrationService;

class SourceNumericFilter extends \Google\Model
{
  /**
   * @var string
   */
  public $numericFilterOption;
  /**
   * @var int
   */
  public $sourceMaxPrecisionFilter;
  /**
   * @var int
   */
  public $sourceMaxScaleFilter;
  /**
   * @var int
   */
  public $sourceMinPrecisionFilter;
  /**
   * @var int
   */
  public $sourceMinScaleFilter;

  /**
   * @param string
   */
  public function setNumericFilterOption($numericFilterOption)
  {
    $this->numericFilterOption = $numericFilterOption;
  }
  /**
   * @return string
   */
  public function getNumericFilterOption()
  {
    return $this->numericFilterOption;
  }
  /**
   * @param int
   */
  public function setSourceMaxPrecisionFilter($sourceMaxPrecisionFilter)
  {
    $this->sourceMaxPrecisionFilter = $sourceMaxPrecisionFilter;
  }
  /**
   * @return int
   */
  public function getSourceMaxPrecisionFilter()
  {
    return $this->sourceMaxPrecisionFilter;
  }
  /**
   * @param int
   */
  public function setSourceMaxScaleFilter($sourceMaxScaleFilter)
  {
    $this->sourceMaxScaleFilter = $sourceMaxScaleFilter;
  }
  /**
   * @return int
   */
  public function getSourceMaxScaleFilter()
  {
    return $this->sourceMaxScaleFilter;
  }
  /**
   * @param int
   */
  public function setSourceMinPrecisionFilter($sourceMinPrecisionFilter)
  {
    $this->sourceMinPrecisionFilter = $sourceMinPrecisionFilter;
  }
  /**
   * @return int
   */
  public function getSourceMinPrecisionFilter()
  {
    return $this->sourceMinPrecisionFilter;
  }
  /**
   * @param int
   */
  public function setSourceMinScaleFilter($sourceMinScaleFilter)
  {
    $this->sourceMinScaleFilter = $sourceMinScaleFilter;
  }
  /**
   * @return int
   */
  public function getSourceMinScaleFilter()
  {
    return $this->sourceMinScaleFilter;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(SourceNumericFilter::class, 'Google_Service_DatabaseMigrationService_SourceNumericFilter');
