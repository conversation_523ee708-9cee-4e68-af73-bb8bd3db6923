<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DatabaseMigrationService;

class ValueListFilter extends \Google\Collection
{
  protected $collection_key = 'values';
  /**
   * @var bool
   */
  public $ignoreCase;
  /**
   * @var string
   */
  public $valuePresentList;
  /**
   * @var string[]
   */
  public $values;

  /**
   * @param bool
   */
  public function setIgnoreCase($ignoreCase)
  {
    $this->ignoreCase = $ignoreCase;
  }
  /**
   * @return bool
   */
  public function getIgnoreCase()
  {
    return $this->ignoreCase;
  }
  /**
   * @param string
   */
  public function setValuePresentList($valuePresentList)
  {
    $this->valuePresentList = $valuePresentList;
  }
  /**
   * @return string
   */
  public function getValuePresentList()
  {
    return $this->valuePresentList;
  }
  /**
   * @param string[]
   */
  public function setValues($values)
  {
    $this->values = $values;
  }
  /**
   * @return string[]
   */
  public function getValues()
  {
    return $this->values;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ValueListFilter::class, 'Google_Service_DatabaseMigrationService_ValueListFilter');
