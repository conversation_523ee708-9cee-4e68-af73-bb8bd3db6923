-- تحديث تعيين المستودعات للمستخدمين
-- هذا الملف يضمن أن جميع المستخدمين لديهم مستودع محدد

-- 1. فحص المستخدمين الذين ليس لديهم مستودع محدد
SELECT 
    u.id,
    u.name,
    u.email,
    u.warehouse_id,
    u.type,
    u.created_by
FROM users u 
WHERE u.warehouse_id IS NULL 
AND u.type IN ('company', 'delivery', 'employee')
ORDER BY u.created_by, u.type;

-- 2. عرض المستودعات المتاحة لكل مؤسسة
SELECT 
    w.id as warehouse_id,
    w.name as warehouse_name,
    w.created_by,
    COUNT(u.id) as users_count
FROM warehouses w
LEFT JOIN users u ON w.id = u.warehouse_id
GROUP BY w.id, w.name, w.created_by
ORDER BY w.created_by, w.name;

-- 3. تحديث المستخدمين ليكون لديهم مستودع افتراضي
-- (قم بتشغيل هذا فقط بعد مراجعة النتائج أعلاه)

-- تحديث المستخدمين الذين ليس لديهم مستودع محدد
-- سيتم تعيين أول مستودع متاح لكل مؤسسة
UPDATE users u
SET warehouse_id = (
    SELECT w.id 
    FROM warehouses w 
    WHERE w.created_by = u.created_by 
    ORDER BY w.id ASC 
    LIMIT 1
)
WHERE u.warehouse_id IS NULL 
AND u.type IN ('company', 'delivery', 'employee')
AND EXISTS (
    SELECT 1 
    FROM warehouses w 
    WHERE w.created_by = u.created_by
);

-- 4. التحقق من النتائج بعد التحديث
SELECT 
    u.id,
    u.name,
    u.email,
    u.warehouse_id,
    w.name as warehouse_name,
    u.type,
    u.created_by
FROM users u 
LEFT JOIN warehouses w ON u.warehouse_id = w.id
WHERE u.type IN ('company', 'delivery', 'employee')
ORDER BY u.created_by, u.type, u.name;

-- 5. إحصائيات نهائية
SELECT 
    'المستخدمين مع مستودع محدد' as status,
    COUNT(*) as count
FROM users 
WHERE warehouse_id IS NOT NULL 
AND type IN ('company', 'delivery', 'employee')

UNION ALL

SELECT 
    'المستخدمين بدون مستودع محدد' as status,
    COUNT(*) as count
FROM users 
WHERE warehouse_id IS NULL 
AND type IN ('company', 'delivery', 'employee');

-- 6. إنشاء مستودع افتراضي للمؤسسات التي ليس لديها مستودعات
-- (اختياري - قم بتشغيله فقط إذا لزم الأمر)

/*
INSERT INTO warehouses (name, address, city, city_zip, created_by, created_at, updated_at)
SELECT 
    CONCAT('المستودع الرئيسي - ', u.name) as name,
    'العنوان الافتراضي' as address,
    'المدينة' as city,
    '12345' as city_zip,
    u.id as created_by,
    NOW() as created_at,
    NOW() as updated_at
FROM users u
WHERE u.type = 'company'
AND u.id NOT IN (
    SELECT DISTINCT created_by 
    FROM warehouses 
    WHERE created_by IS NOT NULL
)
AND u.id NOT IN (
    SELECT DISTINCT created_by 
    FROM warehouses 
    WHERE created_by = u.id
);
*/

-- 7. تحديث المستخدمين الذين لا يزالون بدون مستودع بعد إنشاء المستودعات الجديدة
/*
UPDATE users u
SET warehouse_id = (
    SELECT w.id 
    FROM warehouses w 
    WHERE w.created_by = u.created_by 
    ORDER BY w.id ASC 
    LIMIT 1
)
WHERE u.warehouse_id IS NULL 
AND u.type IN ('company', 'delivery', 'employee');
*/
