<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dataflow;

class CounterMetadata extends \Google\Model
{
  /**
   * @var string
   */
  public $description;
  /**
   * @var string
   */
  public $kind;
  /**
   * @var string
   */
  public $otherUnits;
  /**
   * @var string
   */
  public $standardUnits;

  /**
   * @param string
   */
  public function setDescription($description)
  {
    $this->description = $description;
  }
  /**
   * @return string
   */
  public function getDescription()
  {
    return $this->description;
  }
  /**
   * @param string
   */
  public function setKind($kind)
  {
    $this->kind = $kind;
  }
  /**
   * @return string
   */
  public function getKind()
  {
    return $this->kind;
  }
  /**
   * @param string
   */
  public function setOtherUnits($otherUnits)
  {
    $this->otherUnits = $otherUnits;
  }
  /**
   * @return string
   */
  public function getOtherUnits()
  {
    return $this->otherUnits;
  }
  /**
   * @param string
   */
  public function setStandardUnits($standardUnits)
  {
    $this->standardUnits = $standardUnits;
  }
  /**
   * @return string
   */
  public function getStandardUnits()
  {
    return $this->standardUnits;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(CounterMetadata::class, 'Google_Service_Dataflow_CounterMetadata');
