<?php
/**
 * تحديث تعيين المستودعات للمستخدمين
 * هذا الملف يضمن أن جميع المستخدمين لديهم مستودع محدد
 */

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\warehouse;

echo "🔧 بدء تحديث تعيين المستودعات للمستخدمين...\n\n";

try {
    // 1. فحص المستخدمين الذين ليس لديهم مستودع محدد
    echo "📊 فحص المستخدمين الذين ليس لديهم مستودع محدد:\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    $usersWithoutWarehouse = User::whereNull('warehouse_id')
        ->whereIn('type', ['company', 'delivery', 'employee'])
        ->get(['id', 'name', 'email', 'type', 'created_by']);
    
    if ($usersWithoutWarehouse->count() > 0) {
        foreach ($usersWithoutWarehouse as $user) {
            echo sprintf("ID: %d | الاسم: %s | النوع: %s | المؤسسة: %d\n", 
                $user->id, $user->name, $user->type, $user->created_by);
        }
        echo "\nإجمالي المستخدمين بدون مستودع: " . $usersWithoutWarehouse->count() . "\n\n";
    } else {
        echo "✅ جميع المستخدمين لديهم مستودع محدد!\n\n";
    }

    // 2. عرض المستودعات المتاحة
    echo "🏪 المستودعات المتاحة:\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    $warehouses = warehouse::with(['users' => function($query) {
        $query->whereIn('type', ['company', 'delivery', 'employee']);
    }])->get();
    
    foreach ($warehouses as $warehouse) {
        echo sprintf("ID: %d | الاسم: %s | المؤسسة: %d | المستخدمين: %d\n", 
            $warehouse->id, $warehouse->name, $warehouse->created_by, $warehouse->users->count());
    }
    echo "\nإجمالي المستودعات: " . $warehouses->count() . "\n\n";

    // 3. تحديث المستخدمين
    if ($usersWithoutWarehouse->count() > 0) {
        echo "🔄 بدء تحديث المستخدمين...\n";
        echo "=" . str_repeat("=", 60) . "\n";
        
        $updatedCount = 0;
        $failedCount = 0;
        
        foreach ($usersWithoutWarehouse as $user) {
            // البحث عن أول مستودع متاح للمؤسسة
            $availableWarehouse = warehouse::where('created_by', $user->created_by)
                ->orderBy('id', 'ASC')
                ->first();
            
            if ($availableWarehouse) {
                // تحديث المستخدم
                $user->warehouse_id = $availableWarehouse->id;
                if ($user->save()) {
                    echo sprintf("✅ تم تحديث المستخدم %s (ID: %d) - المستودع: %s (ID: %d)\n", 
                        $user->name, $user->id, $availableWarehouse->name, $availableWarehouse->id);
                    $updatedCount++;
                } else {
                    echo sprintf("❌ فشل تحديث المستخدم %s (ID: %d)\n", $user->name, $user->id);
                    $failedCount++;
                }
            } else {
                echo sprintf("⚠️ لا يوجد مستودع متاح للمستخدم %s (ID: %d) - المؤسسة: %d\n", 
                    $user->name, $user->id, $user->created_by);
                $failedCount++;
            }
        }
        
        echo "\n📈 نتائج التحديث:\n";
        echo "✅ تم تحديث: $updatedCount مستخدم\n";
        echo "❌ فشل التحديث: $failedCount مستخدم\n\n";
    }

    // 4. التحقق النهائي
    echo "🔍 التحقق النهائي:\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    $finalCheck = User::whereIn('type', ['company', 'delivery', 'employee'])
        ->selectRaw('
            SUM(CASE WHEN warehouse_id IS NOT NULL THEN 1 ELSE 0 END) as with_warehouse,
            SUM(CASE WHEN warehouse_id IS NULL THEN 1 ELSE 0 END) as without_warehouse,
            COUNT(*) as total
        ')
        ->first();
    
    echo "👥 إجمالي المستخدمين: " . $finalCheck->total . "\n";
    echo "✅ مع مستودع محدد: " . $finalCheck->with_warehouse . "\n";
    echo "❌ بدون مستودع محدد: " . $finalCheck->without_warehouse . "\n\n";
    
    if ($finalCheck->without_warehouse == 0) {
        echo "🎉 تم تحديث جميع المستخدمين بنجاح!\n";
        echo "💡 الآن يمكن للمستخدمين استخدام نظام POS مع المستودع المثبت تلقائياً.\n\n";
    } else {
        echo "⚠️ لا يزال هناك " . $finalCheck->without_warehouse . " مستخدم بدون مستودع محدد.\n";
        echo "💡 قد تحتاج إلى إنشاء مستودعات إضافية أو تعيين المستودعات يدوياً.\n\n";
    }

    // 5. عرض تفاصيل المستخدمين النهائية
    echo "📋 تفاصيل المستخدمين النهائية:\n";
    echo "=" . str_repeat("=", 80) . "\n";
    
    $finalUsers = User::with('warehouse')
        ->whereIn('type', ['company', 'delivery', 'employee'])
        ->orderBy('created_by')
        ->orderBy('type')
        ->orderBy('name')
        ->get();
    
    foreach ($finalUsers as $user) {
        $warehouseName = $user->warehouse ? $user->warehouse->name : 'غير محدد';
        echo sprintf("%-20s | %-10s | %-25s | المؤسسة: %d\n", 
            $user->name, $user->type, $warehouseName, $user->created_by);
    }

} catch (Exception $e) {
    echo "❌ خطأ في التحديث: " . $e->getMessage() . "\n";
    echo "📍 الملف: " . $e->getFile() . "\n";
    echo "📍 السطر: " . $e->getLine() . "\n";
}

echo "\n🏁 انتهى تحديث تعيين المستودعات.\n";
echo "💡 يمكنك الآن اختبار نظام POS للتأكد من تثبيت المستودع تلقائياً.\n";
