<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DataCatalog;

class GoogleCloudDatacatalogV1DataSource extends \Google\Model
{
  /**
   * @var string
   */
  public $resource;
  /**
   * @var string
   */
  public $service;
  /**
   * @var string
   */
  public $sourceEntry;
  protected $storagePropertiesType = GoogleCloudDatacatalogV1StorageProperties::class;
  protected $storagePropertiesDataType = '';

  /**
   * @param string
   */
  public function setResource($resource)
  {
    $this->resource = $resource;
  }
  /**
   * @return string
   */
  public function getResource()
  {
    return $this->resource;
  }
  /**
   * @param string
   */
  public function setService($service)
  {
    $this->service = $service;
  }
  /**
   * @return string
   */
  public function getService()
  {
    return $this->service;
  }
  /**
   * @param string
   */
  public function setSourceEntry($sourceEntry)
  {
    $this->sourceEntry = $sourceEntry;
  }
  /**
   * @return string
   */
  public function getSourceEntry()
  {
    return $this->sourceEntry;
  }
  /**
   * @param GoogleCloudDatacatalogV1StorageProperties
   */
  public function setStorageProperties(GoogleCloudDatacatalogV1StorageProperties $storageProperties)
  {
    $this->storageProperties = $storageProperties;
  }
  /**
   * @return GoogleCloudDatacatalogV1StorageProperties
   */
  public function getStorageProperties()
  {
    return $this->storageProperties;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudDatacatalogV1DataSource::class, 'Google_Service_DataCatalog_GoogleCloudDatacatalogV1DataSource');
