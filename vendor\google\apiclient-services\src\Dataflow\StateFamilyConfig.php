<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dataflow;

class StateFamilyConfig extends \Google\Model
{
  /**
   * @var bool
   */
  public $isRead;
  /**
   * @var string
   */
  public $stateFamily;

  /**
   * @param bool
   */
  public function setIsRead($isRead)
  {
    $this->isRead = $isRead;
  }
  /**
   * @return bool
   */
  public function getIsRead()
  {
    return $this->isRead;
  }
  /**
   * @param string
   */
  public function setStateFamily($stateFamily)
  {
    $this->stateFamily = $stateFamily;
  }
  /**
   * @return string
   */
  public function getStateFamily()
  {
    return $this->stateFamily;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(StateFamilyConfig::class, 'Google_Service_Dataflow_StateFamilyConfig');
