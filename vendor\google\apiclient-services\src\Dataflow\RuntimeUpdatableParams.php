<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dataflow;

class RuntimeUpdatableParams extends \Google\Model
{
  /**
   * @var int
   */
  public $maxNumWorkers;
  /**
   * @var int
   */
  public $minNumWorkers;
  public $workerUtilizationHint;

  /**
   * @param int
   */
  public function setMaxNumWorkers($maxNumWorkers)
  {
    $this->maxNumWorkers = $maxNumWorkers;
  }
  /**
   * @return int
   */
  public function getMaxNumWorkers()
  {
    return $this->maxNumWorkers;
  }
  /**
   * @param int
   */
  public function setMinNumWorkers($minNumWorkers)
  {
    $this->minNumWorkers = $minNumWorkers;
  }
  /**
   * @return int
   */
  public function getMinNumWorkers()
  {
    return $this->minNumWorkers;
  }
  public function setWorkerUtilizationHint($workerUtilizationHint)
  {
    $this->workerUtilizationHint = $workerUtilizationHint;
  }
  public function getWorkerUtilizationHint()
  {
    return $this->workerUtilizationHint;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(RuntimeUpdatableParams::class, 'Google_Service_Dataflow_RuntimeUpdatableParams');
