<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DatabaseMigrationService;

class SingleColumnChange extends \Google\Collection
{
  protected $collection_key = 'setValues';
  /**
   * @var bool
   */
  public $array;
  /**
   * @var int
   */
  public $arrayLength;
  /**
   * @var bool
   */
  public $autoGenerated;
  /**
   * @var string
   */
  public $charset;
  /**
   * @var string
   */
  public $collation;
  /**
   * @var string
   */
  public $comment;
  /**
   * @var array[]
   */
  public $customFeatures;
  /**
   * @var string
   */
  public $dataType;
  /**
   * @var int
   */
  public $fractionalSecondsPrecision;
  /**
   * @var string
   */
  public $length;
  /**
   * @var bool
   */
  public $nullable;
  /**
   * @var int
   */
  public $precision;
  /**
   * @var int
   */
  public $scale;
  /**
   * @var string[]
   */
  public $setValues;
  /**
   * @var bool
   */
  public $udt;

  /**
   * @param bool
   */
  public function setArray($array)
  {
    $this->array = $array;
  }
  /**
   * @return bool
   */
  public function getArray()
  {
    return $this->array;
  }
  /**
   * @param int
   */
  public function setArrayLength($arrayLength)
  {
    $this->arrayLength = $arrayLength;
  }
  /**
   * @return int
   */
  public function getArrayLength()
  {
    return $this->arrayLength;
  }
  /**
   * @param bool
   */
  public function setAutoGenerated($autoGenerated)
  {
    $this->autoGenerated = $autoGenerated;
  }
  /**
   * @return bool
   */
  public function getAutoGenerated()
  {
    return $this->autoGenerated;
  }
  /**
   * @param string
   */
  public function setCharset($charset)
  {
    $this->charset = $charset;
  }
  /**
   * @return string
   */
  public function getCharset()
  {
    return $this->charset;
  }
  /**
   * @param string
   */
  public function setCollation($collation)
  {
    $this->collation = $collation;
  }
  /**
   * @return string
   */
  public function getCollation()
  {
    return $this->collation;
  }
  /**
   * @param string
   */
  public function setComment($comment)
  {
    $this->comment = $comment;
  }
  /**
   * @return string
   */
  public function getComment()
  {
    return $this->comment;
  }
  /**
   * @param array[]
   */
  public function setCustomFeatures($customFeatures)
  {
    $this->customFeatures = $customFeatures;
  }
  /**
   * @return array[]
   */
  public function getCustomFeatures()
  {
    return $this->customFeatures;
  }
  /**
   * @param string
   */
  public function setDataType($dataType)
  {
    $this->dataType = $dataType;
  }
  /**
   * @return string
   */
  public function getDataType()
  {
    return $this->dataType;
  }
  /**
   * @param int
   */
  public function setFractionalSecondsPrecision($fractionalSecondsPrecision)
  {
    $this->fractionalSecondsPrecision = $fractionalSecondsPrecision;
  }
  /**
   * @return int
   */
  public function getFractionalSecondsPrecision()
  {
    return $this->fractionalSecondsPrecision;
  }
  /**
   * @param string
   */
  public function setLength($length)
  {
    $this->length = $length;
  }
  /**
   * @return string
   */
  public function getLength()
  {
    return $this->length;
  }
  /**
   * @param bool
   */
  public function setNullable($nullable)
  {
    $this->nullable = $nullable;
  }
  /**
   * @return bool
   */
  public function getNullable()
  {
    return $this->nullable;
  }
  /**
   * @param int
   */
  public function setPrecision($precision)
  {
    $this->precision = $precision;
  }
  /**
   * @return int
   */
  public function getPrecision()
  {
    return $this->precision;
  }
  /**
   * @param int
   */
  public function setScale($scale)
  {
    $this->scale = $scale;
  }
  /**
   * @return int
   */
  public function getScale()
  {
    return $this->scale;
  }
  /**
   * @param string[]
   */
  public function setSetValues($setValues)
  {
    $this->setValues = $setValues;
  }
  /**
   * @return string[]
   */
  public function getSetValues()
  {
    return $this->setValues;
  }
  /**
   * @param bool
   */
  public function setUdt($udt)
  {
    $this->udt = $udt;
  }
  /**
   * @return bool
   */
  public function getUdt()
  {
    return $this->udt;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(SingleColumnChange::class, 'Google_Service_DatabaseMigrationService_SingleColumnChange');
