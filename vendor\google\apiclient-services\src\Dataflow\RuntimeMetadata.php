<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dataflow;

class RuntimeMetadata extends \Google\Collection
{
  protected $collection_key = 'parameters';
  protected $parametersType = ParameterMetadata::class;
  protected $parametersDataType = 'array';
  protected $sdkInfoType = SDKInfo::class;
  protected $sdkInfoDataType = '';

  /**
   * @param ParameterMetadata[]
   */
  public function setParameters($parameters)
  {
    $this->parameters = $parameters;
  }
  /**
   * @return ParameterMetadata[]
   */
  public function getParameters()
  {
    return $this->parameters;
  }
  /**
   * @param SDKInfo
   */
  public function setSdkInfo(SDKInfo $sdkInfo)
  {
    $this->sdkInfo = $sdkInfo;
  }
  /**
   * @return SDKInfo
   */
  public function getSdkInfo()
  {
    return $this->sdkInfo;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(RuntimeMetadata::class, 'Google_Service_Dataflow_RuntimeMetadata');
