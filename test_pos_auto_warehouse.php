<?php
/**
 * اختبار تثبيت المستودع التلقائي في نظام POS
 * هذا الملف يختبر أن النظام يعمل بشكل صحيح
 */

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\warehouse;

echo "🧪 اختبار تثبيت المستودع التلقائي في نظام POS\n\n";

try {
    // 1. اختبار المستخدمين مع مستودع ثابت
    echo "✅ اختبار المستخدمين مع مستودع ثابت:\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    $usersWithFixedWarehouse = User::whereNotNull('warehouse_id')
        ->whereIn('type', ['company', 'delivery', 'employee'])
        ->with('warehouse')
        ->get();
    
    if ($usersWithFixedWarehouse->count() > 0) {
        foreach ($usersWithFixedWarehouse as $user) {
            echo "👤 {$user->name} (ID: {$user->id})\n";
            echo "   🏪 المستودع الثابت: {$user->warehouse->name} (ID: {$user->warehouse_id})\n";
            echo "   ✅ النتيجة المتوقعة في POS: المستودع مثبت تلقائياً، لا تظهر قائمة منسدلة\n\n";
        }
    } else {
        echo "⚠️ لا يوجد مستخدمين مع مستودع ثابت\n\n";
    }

    // 2. اختبار المستخدمين مع مستودع واحد فقط
    echo "🔍 اختبار المستخدمين مع مستودع واحد فقط:\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    $companies = User::where('type', 'company')->pluck('id');
    
    foreach ($companies as $companyId) {
        $warehouseCount = warehouse::where('created_by', $companyId)->count();
        $companyUsers = User::where('created_by', $companyId)
            ->whereIn('type', ['company', 'delivery', 'employee'])
            ->get();
        
        echo "🏢 المؤسسة: $companyId\n";
        echo "   📦 عدد المستودعات: $warehouseCount\n";
        
        if ($warehouseCount == 1) {
            $warehouse = warehouse::where('created_by', $companyId)->first();
            echo "   🏪 المستودع الوحيد: {$warehouse->name}\n";
            echo "   ✅ النتيجة المتوقعة: تثبيت تلقائي لجميع مستخدمي هذه المؤسسة\n";
            
            foreach ($companyUsers as $user) {
                $hasCorrectWarehouse = $user->warehouse_id == $warehouse->id;
                $status = $hasCorrectWarehouse ? '✅' : '❌';
                echo "     $status {$user->name}: " . ($hasCorrectWarehouse ? 'مثبت صحيح' : 'يحتاج إصلاح') . "\n";
            }
        } elseif ($warehouseCount > 1) {
            echo "   ⚠️ عدة مستودعات - يحتاج اختيار افتراضي\n";
            $warehouses = warehouse::where('created_by', $companyId)->get();
            foreach ($warehouses as $wh) {
                echo "     - {$wh->name} (ID: {$wh->id})\n";
            }
        } else {
            echo "   ❌ لا يوجد مستودعات - يحتاج إنشاء مستودع\n";
        }
        echo "\n";
    }

    // 3. محاكاة سلوك POS Controller
    echo "🎮 محاكاة سلوك POS Controller:\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    foreach ($usersWithFixedWarehouse->take(3) as $user) {
        echo "👤 محاكاة تسجيل دخول: {$user->name}\n";
        
        // محاكاة منطق PosController
        $allWarehouses = warehouse::where('created_by', $user->creatorId())->pluck('name', 'id');
        $userHasFixedWarehouse = $user->warehouse_id != NULL;
        $onlyOneWarehouse = $allWarehouses->count() == 1;
        $shouldAutoSelectWarehouse = $userHasFixedWarehouse || $onlyOneWarehouse;
        
        echo "   📊 تحليل الحالة:\n";
        echo "     - عدد المستودعات المتاحة: " . $allWarehouses->count() . "\n";
        echo "     - المستخدم له مستودع ثابت: " . ($userHasFixedWarehouse ? 'نعم' : 'لا') . "\n";
        echo "     - مستودع واحد فقط: " . ($onlyOneWarehouse ? 'نعم' : 'لا') . "\n";
        echo "     - يجب التحديد التلقائي: " . ($shouldAutoSelectWarehouse ? 'نعم' : 'لا') . "\n";
        
        if ($shouldAutoSelectWarehouse) {
            if ($userHasFixedWarehouse) {
                $selectedWarehouse = $user->warehouse_id;
                echo "   ✅ المستودع المحدد تلقائياً: " . $allWarehouses[$selectedWarehouse] . " (ID: $selectedWarehouse)\n";
                echo "   🎯 سلوك POS: إخفاء القائمة المنسدلة، عرض اسم المستودع، تحميل المنتجات فوراً\n";
            } else {
                $selectedWarehouse = $allWarehouses->keys()->first();
                echo "   ✅ المستودع المحدد تلقائياً: " . $allWarehouses[$selectedWarehouse] . " (ID: $selectedWarehouse)\n";
                echo "   🎯 سلوك POS: إخفاء القائمة المنسدلة، عرض اسم المستودع، تحميل المنتجات فوراً\n";
            }
        } else {
            echo "   ⚠️ سيتم عرض القائمة المنسدلة للاختيار\n";
        }
        echo "\n";
    }

    // 4. اختبار JavaScript Variables
    echo "🔧 اختبار متغيرات JavaScript:\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    $testUser = $usersWithFixedWarehouse->first();
    if ($testUser) {
        $userHasFixedWarehouse = $testUser->warehouse_id != NULL;
        $allWarehouses = warehouse::where('created_by', $testUser->creatorId())->pluck('name', 'id');
        $onlyOneWarehouse = $allWarehouses->count() == 1;
        $shouldAutoSelectWarehouse = $userHasFixedWarehouse || $onlyOneWarehouse;
        
        echo "للمستخدم: {$testUser->name}\n";
        echo "JavaScript Variables:\n";
        echo "  shouldAutoSelect: " . ($shouldAutoSelectWarehouse ? 'true' : 'false') . "\n";
        echo "  userHasFixedWarehouse: " . ($userHasFixedWarehouse ? 'true' : 'false') . "\n";
        echo "  selectedWarehouse: " . ($testUser->warehouse_id ?: 'null') . "\n\n";
        
        echo "سلوك JavaScript المتوقع:\n";
        if ($shouldAutoSelectWarehouse) {
            echo "  ✅ تحميل المنتجات تلقائياً عند فتح الصفحة\n";
            echo "  ✅ التركيز على حقل البحث بعد 1.5 ثانية\n";
            echo "  ✅ منع تغيير المستودع\n";
            echo "  ✅ عرض رسالة 'تم تثبيت المستودع تلقائياً'\n";
        } else {
            echo "  ⚠️ انتظار اختيار المستودع من المستخدم\n";
        }
    }

    // 5. خلاصة الاختبار
    echo "\n📋 خلاصة الاختبار:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $totalUsers = User::whereIn('type', ['company', 'delivery', 'employee'])->count();
    $usersWithWarehouse = User::whereIn('type', ['company', 'delivery', 'employee'])
        ->whereNotNull('warehouse_id')->count();
    $readyForAutoSelect = 0;
    
    // حساب المستخدمين الجاهزين للتحديد التلقائي
    $companies = User::where('type', 'company')->pluck('id');
    foreach ($companies as $companyId) {
        $warehouseCount = warehouse::where('created_by', $companyId)->count();
        $companyUsers = User::where('created_by', $companyId)
            ->whereIn('type', ['company', 'delivery', 'employee'])
            ->count();
        
        if ($warehouseCount == 1) {
            $readyForAutoSelect += $companyUsers;
        }
    }
    
    $usersWithFixedWarehouseCount = User::whereNotNull('warehouse_id')
        ->whereIn('type', ['company', 'delivery', 'employee'])->count();
    
    echo "📊 إحصائيات النظام:\n";
    echo "  👥 إجمالي المستخدمين: $totalUsers\n";
    echo "  ✅ مع مستودع محدد: $usersWithWarehouse\n";
    echo "  🔧 مع مستودع ثابت: $usersWithFixedWarehouseCount\n";
    echo "  🎯 جاهز للتحديد التلقائي: " . ($usersWithFixedWarehouseCount + $readyForAutoSelect) . "\n\n";
    
    $percentage = $totalUsers > 0 ? round((($usersWithFixedWarehouseCount + $readyForAutoSelect) / $totalUsers) * 100, 1) : 0;
    echo "🎉 نسبة النجاح: $percentage%\n\n";
    
    if ($percentage >= 90) {
        echo "✅ النظام جاهز للعمل مع التثبيت التلقائي!\n";
    } elseif ($percentage >= 70) {
        echo "⚠️ النظام يعمل بشكل جيد، لكن يحتاج بعض التحسينات\n";
    } else {
        echo "❌ النظام يحتاج المزيد من الإعداد\n";
    }

} catch (Exception $e) {
    echo "❌ خطأ في الاختبار: " . $e->getMessage() . "\n";
    echo "📍 الملف: " . $e->getFile() . "\n";
    echo "📍 السطر: " . $e->getLine() . "\n";
}

echo "\n🏁 انتهى اختبار النظام.\n";
