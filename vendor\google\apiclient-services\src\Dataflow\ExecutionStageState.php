<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dataflow;

class ExecutionStageState extends \Google\Model
{
  /**
   * @var string
   */
  public $currentStateTime;
  /**
   * @var string
   */
  public $executionStageName;
  /**
   * @var string
   */
  public $executionStageState;

  /**
   * @param string
   */
  public function setCurrentStateTime($currentStateTime)
  {
    $this->currentStateTime = $currentStateTime;
  }
  /**
   * @return string
   */
  public function getCurrentStateTime()
  {
    return $this->currentStateTime;
  }
  /**
   * @param string
   */
  public function setExecutionStageName($executionStageName)
  {
    $this->executionStageName = $executionStageName;
  }
  /**
   * @return string
   */
  public function getExecutionStageName()
  {
    return $this->executionStageName;
  }
  /**
   * @param string
   */
  public function setExecutionStageState($executionStageState)
  {
    $this->executionStageState = $executionStageState;
  }
  /**
   * @return string
   */
  public function getExecutionStageState()
  {
    return $this->executionStageState;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ExecutionStageState::class, 'Google_Service_Dataflow_ExecutionStageState');
