<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dataflow;

class CPUTime extends \Google\Model
{
  public $rate;
  /**
   * @var string
   */
  public $timestamp;
  /**
   * @var string
   */
  public $totalMs;

  public function setRate($rate)
  {
    $this->rate = $rate;
  }
  public function getRate()
  {
    return $this->rate;
  }
  /**
   * @param string
   */
  public function setTimestamp($timestamp)
  {
    $this->timestamp = $timestamp;
  }
  /**
   * @return string
   */
  public function getTimestamp()
  {
    return $this->timestamp;
  }
  /**
   * @param string
   */
  public function setTotalMs($totalMs)
  {
    $this->totalMs = $totalMs;
  }
  /**
   * @return string
   */
  public function getTotalMs()
  {
    return $this->totalMs;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(CPUTime::class, 'Google_Service_Dataflow_CPUTime');
