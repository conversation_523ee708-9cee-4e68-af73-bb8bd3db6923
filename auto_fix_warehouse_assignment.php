<?php
/**
 * تثبيت المستودع تلقائياً لجميع المستخدمين
 * هذا السكريبت يضمن أن كل مستخدم لديه warehouse_id محدد تلقائياً
 */

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\warehouse;

echo "🔧 بدء تثبيت المستودع تلقائياً لجميع المستخدمين...\n\n";

try {
    // 1. فحص الوضع الحالي
    echo "📊 فحص الوضع الحالي:\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    $totalUsers = User::whereIn('type', ['company', 'delivery', 'employee'])->count();
    $usersWithWarehouse = User::whereIn('type', ['company', 'delivery', 'employee'])
        ->whereNotNull('warehouse_id')->count();
    $usersWithoutWarehouse = $totalUsers - $usersWithWarehouse;
    
    echo "👥 إجمالي المستخدمين: $totalUsers\n";
    echo "✅ مع مستودع محدد: $usersWithWarehouse\n";
    echo "❌ بدون مستودع محدد: $usersWithoutWarehouse\n\n";
    
    if ($usersWithoutWarehouse == 0) {
        echo "🎉 جميع المستخدمين لديهم مستودع محدد بالفعل!\n";
        echo "💡 النظام جاهز للعمل مع التثبيت التلقائي.\n";
        exit;
    }

    // 2. معالجة المستخدمين بدون مستودع
    echo "🔄 معالجة المستخدمين بدون مستودع:\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    $usersWithoutWarehouse = User::whereNull('warehouse_id')
        ->whereIn('type', ['company', 'delivery', 'employee'])
        ->get();
    
    $fixedCount = 0;
    $needsAttentionCount = 0;
    
    foreach ($usersWithoutWarehouse as $user) {
        echo "👤 معالجة المستخدم: {$user->name} (ID: {$user->id})\n";
        
        // البحث عن المستودعات المتاحة
        $availableWarehouses = warehouse::where('created_by', $user->created_by)
            ->orderBy('id', 'ASC')
            ->get();
        
        echo "   📦 المستودعات المتاحة: " . $availableWarehouses->count() . "\n";
        
        if ($availableWarehouses->count() == 1) {
            // مستودع واحد فقط - تثبيته تلقائياً
            $warehouse = $availableWarehouses->first();
            $user->warehouse_id = $warehouse->id;
            
            if ($user->save()) {
                echo "   ✅ تم تثبيت المستودع تلقائياً: {$warehouse->name}\n";
                $fixedCount++;
            } else {
                echo "   ❌ فشل في التحديث\n";
                $needsAttentionCount++;
            }
        } elseif ($availableWarehouses->count() > 1) {
            // عدة مستودعات - تثبيت الأول كافتراضي
            $warehouse = $availableWarehouses->first();
            $user->warehouse_id = $warehouse->id;
            
            if ($user->save()) {
                echo "   ✅ تم تثبيت المستودع الافتراضي: {$warehouse->name}\n";
                echo "   💡 يمكن تغييره لاحقاً إذا لزم الأمر\n";
                $fixedCount++;
            } else {
                echo "   ❌ فشل في التحديث\n";
                $needsAttentionCount++;
            }
        } else {
            // لا يوجد مستودعات - إنشاء مستودع افتراضي
            echo "   ⚠️ لا يوجد مستودعات - إنشاء مستودع افتراضي...\n";
            
            $newWarehouse = warehouse::create([
                'name' => 'المستودع الرئيسي - ' . $user->name,
                'address' => 'العنوان الافتراضي',
                'city' => 'المدينة',
                'city_zip' => '12345',
                'created_by' => $user->created_by
            ]);
            
            if ($newWarehouse) {
                $user->warehouse_id = $newWarehouse->id;
                if ($user->save()) {
                    echo "   ✅ تم إنشاء وتثبيت مستودع جديد: {$newWarehouse->name}\n";
                    $fixedCount++;
                } else {
                    echo "   ❌ فشل في تحديث المستخدم بعد إنشاء المستودع\n";
                    $needsAttentionCount++;
                }
            } else {
                echo "   ❌ فشل في إنشاء المستودع\n";
                $needsAttentionCount++;
            }
        }
        
        echo "\n";
    }

    // 3. النتائج النهائية
    echo "📈 نتائج المعالجة:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    echo "✅ تم إصلاح: $fixedCount مستخدم\n";
    echo "⚠️ يحتاج انتباه: $needsAttentionCount مستخدم\n\n";

    // 4. التحقق النهائي
    echo "🔍 التحقق النهائي:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $finalStats = User::whereIn('type', ['company', 'delivery', 'employee'])
        ->selectRaw('
            COUNT(*) as total,
            SUM(CASE WHEN warehouse_id IS NOT NULL THEN 1 ELSE 0 END) as with_warehouse,
            SUM(CASE WHEN warehouse_id IS NULL THEN 1 ELSE 0 END) as without_warehouse
        ')
        ->first();
    
    echo "👥 إجمالي المستخدمين: " . $finalStats->total . "\n";
    echo "✅ مع مستودع محدد: " . $finalStats->with_warehouse . "\n";
    echo "❌ بدون مستودع محدد: " . $finalStats->without_warehouse . "\n\n";
    
    if ($finalStats->without_warehouse == 0) {
        echo "🎉 تم تثبيت المستودع لجميع المستخدمين بنجاح!\n\n";
        
        echo "✨ الآن في نظام POS:\n";
        echo "   • المستودع سيظهر مثبت تلقائياً\n";
        echo "   • لن تظهر القائمة المنسدلة للاختيار\n";
        echo "   • المنتجات ستحمل فوراً عند فتح الصفحة\n";
        echo "   • المستخدم يمكنه البدء في المسح مباشرة\n\n";
        
        echo "🚀 النظام جاهز للاستخدام مع التثبيت التلقائي الكامل!\n";
    } else {
        echo "⚠️ لا يزال هناك " . $finalStats->without_warehouse . " مستخدم يحتاج معالجة يدوية.\n";
    }

    // 5. عرض تفاصيل المستخدمين النهائية
    echo "\n📋 تفاصيل المستخدمين النهائية:\n";
    echo "=" . str_repeat("=", 80) . "\n";
    
    $allUsers = User::with('warehouse')
        ->whereIn('type', ['company', 'delivery', 'employee'])
        ->orderBy('created_by')
        ->orderBy('name')
        ->get();
    
    $currentCompany = null;
    foreach ($allUsers as $user) {
        if ($currentCompany != $user->created_by) {
            $currentCompany = $user->created_by;
            echo "\n🏢 المؤسسة: $currentCompany\n";
            echo str_repeat("-", 40) . "\n";
        }
        
        $warehouseName = $user->warehouse ? $user->warehouse->name : '❌ غير محدد';
        $status = $user->warehouse_id ? '✅' : '❌';
        
        echo sprintf("  %s %-20s | %-10s | %s\n", 
            $status, $user->name, $user->type, $warehouseName);
    }

} catch (Exception $e) {
    echo "❌ خطأ في المعالجة: " . $e->getMessage() . "\n";
    echo "📍 الملف: " . $e->getFile() . "\n";
    echo "📍 السطر: " . $e->getLine() . "\n";
}

echo "\n🏁 انتهى تثبيت المستودع التلقائي.\n";
echo "💡 يمكنك الآن اختبار نظام POS - المستودع سيكون مثبت تلقائياً!\n";
