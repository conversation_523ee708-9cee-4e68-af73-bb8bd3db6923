<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dataflow;

class WorkerLifecycleEvent extends \Google\Model
{
  /**
   * @var string
   */
  public $containerStartTime;
  /**
   * @var string
   */
  public $event;
  /**
   * @var string[]
   */
  public $metadata;

  /**
   * @param string
   */
  public function setContainerStartTime($containerStartTime)
  {
    $this->containerStartTime = $containerStartTime;
  }
  /**
   * @return string
   */
  public function getContainerStartTime()
  {
    return $this->containerStartTime;
  }
  /**
   * @param string
   */
  public function setEvent($event)
  {
    $this->event = $event;
  }
  /**
   * @return string
   */
  public function getEvent()
  {
    return $this->event;
  }
  /**
   * @param string[]
   */
  public function setMetadata($metadata)
  {
    $this->metadata = $metadata;
  }
  /**
   * @return string[]
   */
  public function getMetadata()
  {
    return $this->metadata;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(WorkerLifecycleEvent::class, 'Google_Service_Dataflow_WorkerLifecycleEvent');
